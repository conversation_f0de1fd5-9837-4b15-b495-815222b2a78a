import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { EyeOff, Users, GitCompareArrows, TimerOff, type LucideIcon } from 'lucide-react';

const CharacteristicCard = ({ icon: Icon, title, enTitle, description, marketingImplications, color }: { icon: LucideIcon, title: string, enTitle: string, description: string, marketingImplications: string, color: string }) => (
    <Card className={`h-full flex flex-col border-t-4 ${color} shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300`}>
        <CardHeader>
            <CardTitle className="flex items-center gap-3 text-2xl text-dark-gray">
                <Icon className={`h-8 w-8 ${color.replace('border-t-', 'text-')}`} />
                {title}
            </CardTitle>
            <p className="text-sm text-slate-500 -mt-2">{enTitle}</p>
        </CardHeader>
        <CardContent className="flex-grow flex flex-col">
            <p className="text-text-gray mb-4 italic leading-relaxed">"{description}"</p>
            <div className="mt-auto">
                <p className="text-sm text-slate-600 bg-slate-50 p-3 rounded-md"><span className="font-semibold text-slate-800">التأثير التسويقي:</span> {marketingImplications}</p>
            </div>
        </CardContent>
    </Card>
);

export const Chapter10NatureAndCharacteristics = () => {
  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-dark-gray">طبيعة وخصائص الخدمات</h3>
      <p className="text-text-gray leading-relaxed">
        الخدمة هي أي نشاط أو منفعة يمكن أن يقدمها طرف لطرف آخر، وهي غير ملموسة بشكل أساسي ولا تؤدي إلى امتلاك أي شيء. هناك أربع خصائص رئيسية للخدمات تميزها عن السلع المادية وتؤثر بشكل كبير على تصميم البرامج التسويقية.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4">
        <CharacteristicCard
          icon={EyeOff}
          title="اللاملموسية"
          enTitle="Intangibility"
          color="border-t-blue-500"
          description="لا يمكن رؤية الخدمات أو تذوقها أو الشعور بها أو سماعها أو شمها قبل شرائها."
          marketingImplications="يجب على المسوقين استخدام 'إشارات' ملموسة للإشارة إلى جودة الخدمة، مثل المكان، والمعدات، والموظفين، والمواد الترويجية."
        />
        <CharacteristicCard
          icon={Users}
          title="التلازمية"
          enTitle="Inseparability"
          color="border-t-green-500"
          description="لا يمكن فصل الخدمات عن مقدميها، سواء كانوا أشخاصًا أم آلات. يتم إنتاج الخدمة واستهلاكها في نفس الوقت."
          marketingImplications="يعتبر مقدم الخدمة جزءًا لا يتجزأ من الخدمة نفسها. تفاعل العميل مع مقدم الخدمة هو ميزة خاصة في تسويق الخدمات."
        />
        <CharacteristicCard
          icon={GitCompareArrows}
          title="التباين"
          enTitle="Variability"
          color="border-t-orange-500"
          description="تعتمد جودة الخدمات بشكل كبير على من يقدمها، ومتى، وأين، وكيف يتم تقديمها."
          marketingImplications="يجب على الشركات الاستثمار في تدريب الموظفين وتوحيد عمليات تقديم الخدمة قدر الإمكان لضمان الجودة المتسقة."
        />
        <CharacteristicCard
          icon={TimerOff}
          title="الفنائية"
          enTitle="Perishability"
          color="border-t-red-500"
          description="لا يمكن تخزين الخدمات لبيعها أو استخدامها لاحقًا. إذا لم يتم استخدام الخدمة، فإن قيمتها تضيع."
          marketingImplications="يجب على الشركات استخدام استراتيجيات متطورة لمواءمة العرض مع الطلب، مثل التسعير التفاضلي، وأنظمة الحجز، وتوظيف موظفين بدوام جزئي خلال فترات الذروة."
        />
      </div>
    </div>
  );
};