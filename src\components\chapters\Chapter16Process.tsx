import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

const StepCard = ({ number, title, children }: { number: number, title: string, children: React.ReactNode }) => (
    <Card className="bg-white shadow-sm">
        <CardHeader>
            <CardTitle className="flex items-start gap-3">
                <span className="flex-shrink-0 h-8 w-8 bg-green-100 text-green-700 font-bold rounded-full flex items-center justify-center text-lg">
                    {number}
                </span>
                <span className="text-2xl text-slate-800">{title}</span>
            </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 pr-12 text-slate-700 leading-relaxed">
            {children}
        </CardContent>
    </Card>
);

export const Chapter16Process = () => {
  return (
    <div className="space-y-6">
      <p>
        تتبع عملية الشراء في المنظمات سلسلة من الخطوات المنهجية لضمان اتخاذ قرارات مدروسة وفعالة. هذه العملية مشابهة لعملية الشراء الصناعي التي تمت مناقشتها في الفصل الثامن.
      </p>
      
      <div className="space-y-6 pt-6">
        <StepCard number={1} title="إدراك الحاجة (Need Recognition)">
          <p>تبدأ العملية عندما تدرك جهة ما داخل المنظمة (مثل قسم الإنتاج أو الصيانة) وجود حاجة لمادة أو خدمة معينة.</p>
        </StepCard>
        <StepCard number={2} title="وصف الحاجة وتحديد المواصفات">
          <p>يتم تحديد خصائص وكمية العنصر المطلوب بشكل دقيق. هذه الخطوة حاسمة لضمان الحصول على المنتج الصحيح الذي يلبي المتطلبات الفنية.</p>
        </StepCard>
        <StepCard number={3} title="البحث عن الموردين المحتملين">
          <p>يقوم قسم المشتريات بالبحث عن الموردين القادرين على تلبية الحاجة المحددة، وذلك من خلال قواعد البيانات، المعارض التجارية، أو شبكة العلاقات.</p>
        </StepCard>
        <StepCard number={4} title="التماس وتقييم العروض">
          <p>يتم دعوة الموردين المؤهلين لتقديم عروضهم. يقوم فريق المشتريات بتقييم هذه العروض بناءً على معايير محددة مسبقًا مثل السعر، الجودة، وقت التسليم، والخدمة.</p>
        </StepCard>
        <StepCard number={5} title="اختيار المورد والتفاوض">
          <p>بعد تقييم العروض، يتم اختيار المورد الأنسب. قد تتبع هذه الخطوة مرحلة تفاوض على الشروط النهائية للعقد.</p>
        </StepCard>
        <StepCard number={6} title="إصدار أمر الشراء">
          <p>يتم إصدار وثيقة رسمية (أمر الشراء) تحدد جميع تفاصيل الصفقة، بما في ذلك المواصفات، الكمية، السعر، وشروط التسليم والدفع.</p>
        </StepCard>
        <StepCard number={7} title="متابعة الطلب واستلامه">
          <p>يقوم قسم المشتريات بمتابعة الطلب لضمان التسليم في الوقت المحدد. عند وصول الشحنة، يتم فحصها للتأكد من مطابقتها للمواصفات.</p>
        </StepCard>
        <StepCard number={8} title="تقييم أداء المورد">
          <p>بعد إتمام الصفقة، يتم تقييم أداء المورد. هذه المعلومات تستخدم في قرارات الشراء المستقبلية وتساعد في إدارة علاقات الموردين.</p>
        </StepCard>
      </div>
    </div>
  );
};