{"name": "marketing-pro-desktop", "version": "1.0.0", "description": "A desktop application for Marketing Management course.", "author": "Your Name", "main": "electron/main.cjs", "homepage": "./", "scripts": {"dev": "vite", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:8080 && electron .\"", "build": "vite build", "electron:build": "npm run build && electron-builder"}, "build": {"appId": "com.dyad.marketingapp", "productName": "MarketingApp", "directories": {"output": "dist_electron"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}}, "dependencies": {"@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-query": "^5.56.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html2pdf.js": "^0.10.3", "lucide-react": "^0.462.0", "next-themes": "^0.4.6", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.62.0", "react-router-dom": "^6.30.1", "sonner": "^2.0.7", "tailwind-merge": "^2.5.2", "zod": "^3.25.76"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.12", "@vitejs/plugin-react-swc": "^3.9.0", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "electron": "^31.7.7", "electron-builder": "^25.1.8", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "typescript": "^5.9.2", "vite": "^6.3.5", "wait-on": "^7.2.0"}}