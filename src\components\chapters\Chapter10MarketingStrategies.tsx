import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Link, Users, Settings, Eye, type LucideIcon } from 'lucide-react';

const ServiceProfitChainDiagram = () => (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-6 text-slate-800">سلسلة الربح الخدمي</h4>
        <div className="flex flex-col md:flex-row-reverse items-center justify-center gap-2 text-center text-sm">
            <div className="p-2 border rounded-md shadow-sm bg-white w-full md:w-auto">جودة الخدمة الداخلية</div>
            <div className="text-xl text-slate-500 mx-2">←</div>
            <div className="p-2 border rounded-md shadow-sm bg-white w-full md:w-auto">رضا وإنتاجية الموظفين</div>
            <div className="text-xl text-slate-500 mx-2">←</div>
            <div className="p-2 border rounded-md shadow-sm bg-white w-full md:w-auto">قيمة خدمة أكبر</div>
            <div className="text-xl text-slate-500 mx-2">←</div>
            <div className="p-2 border rounded-md shadow-sm bg-white w-full md:w-auto">رضا وولاء العملاء</div>
            <div className="text-xl text-slate-500 mx-2">←</div>
            <div className="p-2 border rounded-md shadow-sm bg-green-100 border-green-300 text-green-800 w-full md:w-auto">أرباح ونمو صحي</div>
        </div>
    </div>
);

const ExtendedMixCard = ({ icon: Icon, title, enTitle, description, color }: { icon: LucideIcon, title: string, enTitle: string, description: string, color: string }) => (
    <Card className={`h-full flex flex-col border-t-4 ${color} shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300`}>
        <CardHeader>
            <CardTitle className="flex items-center gap-3 text-2xl text-dark-gray">
                <Icon className={`h-8 w-8 ${color.replace('border-t-', 'text-')}`} />
                {title}
            </CardTitle>
            <p className="text-sm text-slate-500 -mt-2">{enTitle}</p>
        </CardHeader>
        <CardContent className="flex-grow">
            <p className="text-text-gray leading-relaxed">{description}</p>
        </CardContent>
    </Card>
);

export const Chapter10MarketingStrategies = () => {
  return (
    <div className="space-y-8">
      <Card className="bg-white shadow-none border-0">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-slate-800 flex items-center gap-3">
            <Link className="h-8 w-8 text-cyan-700" />
            استراتيجيات التسويق للشركات الخدمية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
          <p>
            تمامًا مثل شركات التصنيع، تستخدم شركات الخدمات استراتيجيات التسويق. لكن بسبب طبيعة الخدمات الفريدة، تتطلب استراتيجيات إضافية. في الأعمال الخدمية، يعتمد العميل والموظف الأمامي على التفاعل لإنشاء الخدمة. لذلك، يجب على المسوقين الخدميين جعل الموظفين يؤمنون بالعلامة التجارية حتى يتمكنوا من تقديمها بشكل أصيل للعملاء.
          </p>
          <h4 className="font-bold text-xl pt-4 border-t">سلسلة الربح الخدمي (The Service Profit Chain)</h4>
          <p>
            تربط هذه السلسلة أرباح الشركة الخدمية برضا الموظفين والعملاء. تتكون من خمس حلقات: تبدأ بجودة الخدمة الداخلية، التي تؤدي إلى موظفين راضين ومنتجين، مما يخلق قيمة خدمة أكبر، وهذا يؤدي إلى عملاء راضين وموالين، وفي النهاية، يحقق أرباحًا ونموًا صحيًا للشركة.
          </p>
        </CardContent>
      </Card>
      
      <ServiceProfitChainDiagram />

      <div className="pt-8 border-t">
        <h3 className="text-2xl font-bold text-dark-gray">المزيج التسويقي الموسع للخدمات (7Ps)</h3>
        <p className="text-text-gray leading-relaxed mt-2 mb-6">
          بالإضافة إلى المزيج التسويقي التقليدي (المنتج، السعر، المكان، الترويج)، يتضمن تسويق الخدمات ثلاثة عناصر إضافية حاسمة:
        </p>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <ExtendedMixCard
                icon={Users}
                title="الأفراد"
                enTitle="People"
                color="border-t-purple-500"
                description="جميع الأشخاص المشاركين بشكل مباشر أو غير مباشر في تقديم الخدمة، من موظفي الخطوط الأمامية إلى الإدارة. يلعبون دورًا حيويًا في تشكيل تصور العميل عن الخدمة."
            />
            <ExtendedMixCard
                icon={Settings}
                title="العمليات"
                enTitle="Process"
                color="border-t-orange-500"
                description="الإجراءات والآليات وتدفق الأنشطة التي يتم من خلالها تقديم الخدمة. تؤثر كفاءة وسلاسة العمليات بشكل مباشر على تجربة العميل."
            />
            <ExtendedMixCard
                icon={Eye}
                title="الدليل المادي"
                enTitle="Physical Evidence"
                color="border-t-teal-500"
                description="البيئة التي يتم فيها تقديم الخدمة وأي مكونات ملموسة تسهل أداء الخدمة أو توصيلها. يساعد الدليل المادي في 'جعل الخدمة ملموسة'."
            />
        </div>
      </div>
    </div>
  );
};