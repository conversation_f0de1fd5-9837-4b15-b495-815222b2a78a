import React from 'react';
import { Card, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";

const StepCard = ({ number, title, children }: { number: number, title: string, children: React.ReactNode }) => (
    <Card className="bg-white shadow-sm">
        <CardHeader>
            <CardTitle className="flex items-start gap-3">
                <span className="flex-shrink-0 h-8 w-8 bg-gray-100 text-gray-700 font-bold rounded-full flex items-center justify-center text-lg">
                    {number}
                </span>
                <span className="text-2xl text-slate-800">{title}</span>
            </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 pr-12 text-slate-700 leading-relaxed">
            {children}
        </CardContent>
    </Card>
);

export const Chapter8BuyingProcess = () => {
  return (
    <div className="space-y-6">
      <p>
        تتكون عملية الشراء في منظمات الأعمال من ثماني مراحل رئيسية. في مواقف إعادة الشراء المعدل أو المباشر، قد يتخطى المشترون بعض هذه المراحل. ولكن في موقف المهمة الجديدة، يمر المشتري عادة بجميع المراحل.
      </p>
      
      <div className="space-y-6 pt-6">
        <StepCard number={1} title="إدراك المشكلة (Problem Recognition)">
          <p>تبدأ العملية عندما يدرك شخص ما في الشركة وجود مشكلة أو حاجة يمكن تلبيتها عن طريق الحصول على منتج أو خدمة. يمكن أن يكون هذا الإدراك ناتجًا عن محفزات داخلية (مثل الحاجة إلى معدات جديدة) أو خارجية (مثل رؤية إعلان عن منتج أفضل).</p>
        </StepCard>
        <StepCard number={2} title="وصف الحاجة العام (General Need Description)">
          <p>بعد إدراك الحاجة، تقوم الشركة بوصف خصائص وكمية العنصر المطلوب. بالنسبة للعناصر المعقدة، قد يعمل المشتري مع المهندسين والمستخدمين لتحديد الحاجة.</p>
        </StepCard>
        <StepCard number={3} title="تحديد مواصفات المنتج (Product Specification)">
          <p>تقوم المنظمة المشترية بتطوير المواصفات الفنية للمنتج المطلوب، غالبًا بمساعدة فريق هندسي. يقوم تحليل القيمة (Value Analysis) بتحديد المكونات التي يمكن إعادة تصميمها أو توحيدها لخفض التكاليف.</p>
        </StepCard>
        <StepCard number={4} title="البحث عن المجهزين (Supplier Search)">
          <p>يقوم المشتري بالبحث عن أفضل المجهزين المؤهلين من خلال دلائل التجارة، البحث عبر الإنترنت، أو توصيات الآخرين.</p>
        </StepCard>
        <StepCard number={5} title="التماس العروض (Proposal Solicitation)">
          <p>يدعو المشتري المجهزين المؤهلين لتقديم عروضهم. استجابةً لذلك، يقدم المجهزون عروضهم التي يجب أن تكون وثائق تسويقية مقنعة، وليس مجرد وثائق فنية.</p>
        </StepCard>
        <StepCard number={6} title="اختيار المجهز (Supplier Selection)">
          <p>يقوم أعضاء مركز الشراء بمراجعة العروض واختيار مجهز واحد أو أكثر. يتم تقييم المجهزين بناءً على سمات متعددة مثل جودة المنتج، السمعة، التسليم في الوقت المحدد، السلوك الأخلاقي، والأسعار.</p>
        </StepCard>
        <StepCard number={7} title="تحديد روتين الطلب (Order-Routine Specification)">
          <p>يقوم المشتري بإعداد الطلب النهائي مع المجهز المختار، مع تحديد المواصفات الفنية، الكمية المطلوبة، وقت التسليم المتوقع، سياسات الإرجاع، والضمانات.</p>
        </StepCard>
        <StepCard number={8} title="مراجعة الأداء (Performance Review)">
          <p>في هذه المرحلة، يقوم المشتري بتقييم أداء المجهز ويقرر ما إذا كان سيستمر في التعامل معه، أو يعدل العلاقة، أو ينهيها. قد يطلب المشتري تقييمات من المستخدمين ويقوم بتقييم المجهز بناءً على معايير متعددة.</p>
        </StepCard>
      </div>
    </div>
  );
};