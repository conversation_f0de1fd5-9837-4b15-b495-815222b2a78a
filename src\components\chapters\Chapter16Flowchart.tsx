import React from 'react';
import { ArrowDown } from 'lucide-react';

const FlowBox = ({ children, className = '', level = 1 }: { children: React.ReactNode; className?: string; level?: number }) => {
  const levelClasses = {
    1: 'bg-green-600 border-green-800 text-white font-bold text-lg',
    2: 'bg-green-200 border-green-400 text-green-800 font-semibold',
    3: 'bg-green-100 border-green-300 text-green-700 text-sm',
  };
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-2 text-center shadow-lg flex items-center justify-center min-h-[45px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 ${levelClasses[level]} ${className}`}>
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => <ArrowDown className="h-6 w-6 text-slate-400 mx-auto my-1" />;

export const Chapter16Flowchart = () => {
  return (
    <div dir="rtl" className="p-4 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-6 text-green-800">
        هيكلية الفصل السادس عشر
      </h3>
      <div className="flex flex-col items-center">
        <FlowBox level={1} className="max-w-sm">المشتريات وإدارة التوريد</FlowBox>
        <VerticalArrow />
        
        <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-6">
          
          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>مفهوم وأهمية المشتريات</FlowBox>
            <VerticalArrow />
            <FlowBox level={2}>أهداف المشتريات</FlowBox>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>عملية الشراء</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-green-50 rounded-md border-2 border-dashed border-green-200">
              <FlowBox level={3}>إدراك الحاجة</FlowBox>
              <FlowBox level={3}>وصف الحاجة</FlowBox>
              <FlowBox level={3}>تحديد المواصفات</FlowBox>
              <FlowBox level={3}>البحث عن موردين</FlowBox>
              <FlowBox level={3}>التماس العروض</FlowBox>
              <FlowBox level={3}>اختيار المورد</FlowBox>
              <FlowBox level={3}>تحديد روتين الطلب</FlowBox>
              <FlowBox level={3}>مراجعة الأداء</FlowBox>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>أنواع الشراء</FlowBox>
            <VerticalArrow />
            <FlowBox level={2}>الشراء الإلكتروني</FlowBox>
          </div>

        </div>
      </div>
    </div>
  );
};