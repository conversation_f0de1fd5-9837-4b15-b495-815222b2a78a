import React from 'react';
import { ArrowDown } from 'lucide-react';

const FlowBox = ({ children, className = '', level = 1 }: { children: React.ReactNode; className?: string; level?: number }) => {
  const levelClasses = {
    1: 'bg-gray-700 border-gray-900 text-white font-bold text-lg',
    2: 'bg-gray-300 border-gray-500 text-gray-800 font-semibold',
    3: 'bg-gray-200 border-gray-400 text-gray-700 text-sm',
  };
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-2 text-center shadow-lg flex items-center justify-center min-h-[45px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 ${levelClasses[level]} ${className}`}>
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => <ArrowDown className="h-6 w-6 text-slate-400 mx-auto my-1" />;

export const Chapter8Flowchart = () => {
  return (
    <div dir="rtl" className="p-4 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-6 text-gray-800">
        هيكلية الفصل الثامن
      </h3>
      <div className="flex flex-col items-center">
        <FlowBox level={1} className="max-w-sm">سوق منظمات الأعمال وسلوك المشتري الصناعي</FlowBox>
        <VerticalArrow />
        
        <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-6">
          
          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>سوق منظمات الأعمال</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-gray-50 rounded-md border-2 border-dashed border-gray-200">
              <FlowBox level={3}>خصائص أسواق الأعمال</FlowBox>
              <FlowBox level={3}>نموذج سلوك المشتري الصناعي</FlowBox>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>سلوك المشتري الصناعي</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-gray-50 rounded-md border-2 border-dashed border-gray-200">
              <FlowBox level={3}>أنواع مواقف الشراء</FlowBox>
              <FlowBox level={3}>المشاركون في عملية الشراء</FlowBox>
              <FlowBox level={3}>المؤثرات الرئيسية على المشترين</FlowBox>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>عملية الشراء الصناعي</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-gray-50 rounded-md border-2 border-dashed border-gray-200">
              <FlowBox level={3}>مراحل عملية الشراء</FlowBox>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};