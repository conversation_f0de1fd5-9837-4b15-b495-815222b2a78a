import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Chapter10Flowchart } from './Chapter10Flowchart';
import { Chapter10Introduction } from './Chapter10Introduction';
import { Chapter10NatureAndCharacteristics } from './Chapter10NatureAndCharacteristics';
import { Chapter10MarketingStrategies } from './Chapter10MarketingStrategies';
import { BookOpen, Share2, Wrench, SlidersHorizontal, TestTube2 } from 'lucide-react';
import { Quiz } from "@/components/Quiz";
import { quizzes } from "@/data/quizzes";

// Find the quiz for chapter 10
const chapter10Quiz = quizzes.find(q => q.chapterId === 10);

// Define sections for Chapter 10
export const chapter10Sections = [
  { value: "item-0", title: "هيكلية الفصل", icon: Share2, component: <Chapter10Flowchart /> },
  { value: "item-1", title: "مقدمة الفصل", icon: BookOpen, component: <Chapter10Introduction /> },
  { value: "item-2", title: "طبيعة وخصائص الخدمات", icon: Wrench, component: <Chapter10NatureAndCharacteristics /> },
  { value: "item-3", title: "استراتيجيات تسويق الخدمات", icon: SlidersHorizontal, component: <Chapter10MarketingStrategies /> },
  {
    value: "item-quiz",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: chapter10Quiz ? <Quiz questions={chapter10Quiz.questions} chapterId={10} /> : <p>لا يوجد اختبار متاح حاليًا.</p>,
  },
];

interface Chapter10Props {
  sections: typeof chapter10Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean;
}

export const Chapter10 = ({ sections, activeSectionValue, isPresentationMode }: Chapter10Props) => {
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-1")}
      value={isPresentationMode ? activeSectionValue : undefined}
      className="w-full space-y-4"
    >
      {sections.map((section) => (
        <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
          <AccordionTrigger className="p-6 text-xl font-bold text-slate-700 hover:no-underline data-[state=open]:bg-slate-50">
            <div className="flex items-center gap-4">
              <section.icon className="h-7 w-7 text-cyan-600 flex-shrink-0" />
              <span className="text-right">{section.title}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-6 pt-2 bg-white">
            {section.component}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};