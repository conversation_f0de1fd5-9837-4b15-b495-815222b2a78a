import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Chapter15Flowchart } from './Chapter15Flowchart';
import { Chapter15Introduction } from './Chapter15Introduction';
import { Chapter15NatureOfSelling } from './Chapter15NatureOfSelling';
import { Chapter15SalesManagement } from './Chapter15SalesManagement';
import { Chapter15SalesProcess } from './Chapter15SalesProcess';
import { BookOpen, Share2, UserCheck, Users, ListOrdered, TestTube2 } from 'lucide-react';
import { Quiz } from "@/components/Quiz";
import { quizzes } from "@/data/quizzes";

// Find the quiz for chapter 15
const chapter15Quiz = quizzes.find(q => q.chapterId === 15);

// Define sections for Chapter 15
export const chapter15Sections = [
  { value: "item-0", title: "هيكلية الفصل", icon: Share2, component: <Chapter15Flowchart /> },
  { value: "item-1", title: "مقدمة الفصل", icon: BookOpen, component: <Chapter15Introduction /> },
  { value: "item-2", title: "طبيعة البيع الشخصي ودور قوى البيع", icon: UserCheck, component: <Chapter15NatureOfSelling /> },
  { value: "item-3", title: "إدارة قوى البيع", icon: Users, component: <Chapter15SalesManagement /> },
  { value: "item-4", title: "خطوات عملية البيع الشخصي", icon: ListOrdered, component: <Chapter15SalesProcess /> },
  {
    value: "item-quiz",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: chapter15Quiz ? <Quiz questions={chapter15Quiz.questions} chapterId={15} /> : <p>لا يوجد اختبار متاح حاليًا.</p>,
  },
];

interface Chapter15Props {
  sections: typeof chapter15Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean;
}

export const Chapter15 = ({ sections, activeSectionValue, isPresentationMode }: Chapter15Props) => {
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-1")}
      value={isPresentationMode ? activeSectionValue : undefined}
      className="w-full space-y-4"
    >
      {sections.map((section) => (
        <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
          <AccordionTrigger className="p-6 text-xl font-bold text-slate-700 hover:no-underline data-[state=open]:bg-slate-50">
            <div className="flex items-center gap-4">
              <section.icon className="h-7 w-7 text-slate-600 flex-shrink-0" />
              <span className="text-right">{section.title}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-6 pt-2 bg-white">
            {section.component}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};