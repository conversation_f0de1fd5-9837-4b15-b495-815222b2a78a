import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Chapter16Flowchart } from './Chapter16Flowchart';
import { Chapter16Introduction } from './Chapter16Introduction';
import { Chapter16Concept } from './Chapter16Concept';
import { Chapter16Objectives } from './Chapter16Objectives';
import { Chapter16Process } from './Chapter16Process';
import { BookOpen, Share2, ShoppingCart, Target, ListOrdered, TestTube2 } from 'lucide-react';
import { Quiz } from "@/components/Quiz";
import { quizzes } from "@/data/quizzes";

// Find the quiz for chapter 16
const chapter16Quiz = quizzes.find(q => q.chapterId === 16);

// Define sections for Chapter 16
export const chapter16Sections = [
  { value: "item-0", title: "هيكلية الفصل", icon: Share2, component: <Chapter16Flowchart /> },
  { value: "item-1", title: "مقدمة الفصل", icon: BookOpen, component: <Chapter16Introduction /> },
  { value: "item-2", title: "مفهوم وأهمية المشتريات", icon: ShoppingCart, component: <Chapter16Concept /> },
  { value: "item-3", title: "أهداف إدارة المشتريات", icon: Target, component: <Chapter16Objectives /> },
  { value: "item-4", title: "عملية الشراء", icon: ListOrdered, component: <Chapter16Process /> },
  {
    value: "item-quiz",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: chapter16Quiz ? <Quiz questions={chapter16Quiz.questions} chapterId={16} /> : <p>لا يوجد اختبار متاح حاليًا.</p>,
  },
];

interface Chapter16Props {
  sections: typeof chapter16Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean;
}

export const Chapter16 = ({ sections, activeSectionValue, isPresentationMode }: Chapter16Props) => {
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-1")}
      value={isPresentationMode ? activeSectionValue : undefined}
      className="w-full space-y-4"
    >
      {sections.map((section) => (
        <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
          <AccordionTrigger className="p-6 text-xl font-bold text-slate-700 hover:no-underline data-[state=open]:bg-slate-50">
            <div className="flex items-center gap-4">
              <section.icon className="h-7 w-7 text-green-600 flex-shrink-0" />
              <span className="text-right">{section.title}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-6 pt-2 bg-white">
            {section.component}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};