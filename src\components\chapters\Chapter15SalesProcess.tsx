import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Search, Handshake, Presentation, MessageSquare, Check, Star, Repeat } from 'lucide-react';

const ProcessStep = ({ number, title, icon: Icon, children }: { number: number, title: string, icon: React.ElementType, children: React.ReactNode }) => (
    <Card className="bg-white shadow-sm">
        <CardHeader>
            <CardTitle className="flex items-start gap-3">
                <span className="flex-shrink-0 h-8 w-8 bg-slate-100 text-slate-700 font-bold rounded-full flex items-center justify-center text-lg">
                    {number}
                </span>
                <div className="flex items-center gap-2">
                    <Icon className="h-6 w-6 text-slate-600" />
                    <span className="text-2xl text-slate-800">{title}</span>
                </div>
            </CardTitle>
        </CardHeader>
        <CardContent className="pr-12 text-slate-700 leading-relaxed">
            {children}
        </CardContent>
    </Card>
);

export const Chapter15SalesProcess = () => {
  return (
    <div className="space-y-6">
      <Card className="bg-white shadow-none border-0">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-slate-800">
            خطوات عملية البيع الشخصي
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
          <p>
            تتكون عملية البيع من سبع خطوات رئيسية. تهدف هذه الخطوات إلى مساعدة مندوبي المبيعات على إغلاق صفقات محددة وبناء علاقات مربحة مع العملاء.
          </p>
        </CardContent>
      </Card>

      <div className="space-y-6 pt-6 border-t">
        <ProcessStep number={1} title="البحث عن العملاء المحتملين والتأهيل" icon={Search}>
            <p>الخطوة الأولى هي تحديد العملاء المحتملين المؤهلين. يتم ذلك من خلال البحث عن العملاء المحتملين (Prospecting) ثم تأهيلهم (Qualifying) للتأكد من أن لديهم الحاجة، السلطة، والقدرة المالية على الشراء.</p>
        </ProcessStep>
        <ProcessStep number={2} title="التحضير للمقابلة (Preapproach)" icon={Handshake}>
            <p>قبل الاتصال بالعميل المحتمل، يجب على مندوب المبيعات أن يتعلم قدر الإمكان عن المنظمة (ماذا تحتاج، من يشارك في الشراء) والمشترين (خصائصهم وأساليب شرائهم).</p>
        </ProcessStep>
        <ProcessStep number={3} title="المقاربة (Approach)" icon={Presentation}>
            <p>في هذه الخطوة، يلتقي مندوب المبيعات بالعميل لأول مرة. يجب أن يعرف كيفية تحية المشتري وبدء العلاقة بشكل جيد. المظهر، عبارات الافتتاح، والمتابعة كلها أمور مهمة.</p>
        </ProcessStep>
        <ProcessStep number={4} title="العرض التقديمي والإيضاحي" icon={MessageSquare}>
            <p>يقوم مندوب المبيعات بـ "سرد قصة القيمة" للمشتري، موضحًا كيف يحل عرض الشركة مشاكل العميل. الهدف هو إظهار كيف يمكن للمنتج أو الخدمة أن تخلق قيمة أكبر للعميل.</p>
        </ProcessStep>
        <ProcessStep number={5} title="التعامل مع الاعتراضات" icon={Check}>
            <p>يجب على مندوب المبيعات استخدام نهج إيجابي، البحث عن الاعتراضات الخفية، مطالبة المشتري بتوضيح اعتراضاته، واستخدام الاعتراضات كفرص لتقديم المزيد من المعلومات.</p>
        </ProcessStep>
        <ProcessStep number={6} title="إغلاق الصفقة (Closing)" icon={Star}>
            <p>بعد التعامل مع الاعتراضات، يحاول مندوب المبيعات إغلاق الصفقة. يجب أن يعرفوا كيفية التعرف على إشارات الإغلاق من المشتري، بما في ذلك الحركات الجسدية، التعليقات، والأسئلة.</p>
        </ProcessStep>
        <ProcessStep number={7} title="المتابعة (Follow-up)" icon={Repeat}>
            <p>المتابعة ضرورية لضمان رضا العملاء وتكرار الأعمال. يجب على مندوب المبيعات جدولة مكالمة متابعة بعد تسليم الطلب لضمان التركيب والتشغيل والتدريب بشكل صحيح.</p>
        </ProcessStep>
      </div>
    </div>
  );
};