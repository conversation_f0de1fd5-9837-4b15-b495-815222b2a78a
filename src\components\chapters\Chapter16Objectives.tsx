import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Target, DollarSign, ShieldCheck, Link, Lightbulb } from 'lucide-react';

const ObjectiveCard = ({ icon: Icon, title, children, color }: { icon: React.ElementType; title: string; children: React.ReactNode; color: string }) => (
    <Card className={`shadow-md border-t-4 ${color}`}>
        <CardHeader>
            <CardTitle className="flex items-center gap-3 text-2xl text-slate-800">
                <Icon className={`h-8 w-8 ${color.replace('border-t-', 'text-')}`} />
                {title}
            </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4 text-slate-700 leading-relaxed">
            {children}
        </CardContent>
    </Card>
);

export const Chapter16Objectives = () => {
  return (
    <div className="space-y-8">
      <Card className="bg-white shadow-none border-0">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-slate-800 flex items-center gap-3">
            <Target className="h-8 w-8 text-green-700" />
            أهداف إدارة المشتريات
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
          <p>
            تسعى إدارة المشتريات إلى تحقيق مجموعة من الأهداف المتكاملة التي تخدم الأهداف العامة للمنظمة. يمكن تقسيم هذه الأهداف إلى أهداف أساسية وأهداف ثانوية.
          </p>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <ObjectiveCard icon={DollarSign} title="1. تحقيق أفضل قيمة مقابل المال" color="border-t-blue-500">
            <p>لا يعني هذا دائمًا الحصول على أقل سعر، بل تحقيق أفضل مزيج من الجودة، الخدمة، السعر، والتسليم. الهدف هو تقليل التكلفة الإجمالية للملكية (Total Cost of Ownership - TCO).</p>
        </ObjectiveCard>
        <ObjectiveCard icon={ShieldCheck} title="2. ضمان استمرارية التوريد" color="border-t-green-500">
            <p>ضمان تدفق مستمر وغير منقطع للمواد والخدمات اللازمة لعمليات المنظمة، وتجنب أي توقف في الإنتاج بسبب نقص المواد.</p>
        </ObjectiveCard>
        <ObjectiveCard icon={Link} title="3. تطوير علاقات فعالة مع الموردين" color="border-t-purple-500">
            <p>بناء علاقات تعاونية وقوية مع الموردين الموثوقين، مما يضمن الحصول على دعمهم وولائهم ويشجع على الابتكار المشترك.</p>
        </ObjectiveCard>
        <ObjectiveCard icon={Lightbulb} title="4. دعم أهداف المنظمة" color="border-t-orange-500">
            <p>المساهمة في تحقيق الأهداف الاستراتيجية للمنظمة، مثل دعم تطوير المنتجات الجديدة، تحقيق أهداف الاستدامة، أو دخول أسواق جديدة من خلال البحث عن موردين عالميين.</p>
        </ObjectiveCard>
      </div>
    </div>
  );
};