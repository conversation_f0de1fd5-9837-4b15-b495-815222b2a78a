import React from 'react';
import { ArrowDown } from 'lucide-react';

const FlowBox = ({ children, className = '', level = 1 }: { children: React.ReactNode; className?: string; level?: number }) => {
  const levelClasses = {
    1: 'bg-cyan-600 border-cyan-800 text-white font-bold text-lg',
    2: 'bg-cyan-200 border-cyan-400 text-cyan-800 font-semibold',
    3: 'bg-cyan-100 border-cyan-300 text-cyan-700 text-sm',
  };
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-2 text-center shadow-lg flex items-center justify-center min-h-[45px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 ${levelClasses[level]} ${className}`}>
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => <ArrowDown className="h-6 w-6 text-slate-400 mx-auto my-1" />;

export const Chapter10Flowchart = () => {
  return (
    <div dir="rtl" className="p-4 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-6 text-cyan-800">
        هيكلية الفصل العاشر
      </h3>
      <div className="flex flex-col items-center">
        <FlowBox level={1} className="max-w-sm">الخدمات</FlowBox>
        <VerticalArrow />
        
        <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-6">
          
          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>طبيعة وخصائص الخدمات</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-cyan-50 rounded-md border-2 border-dashed border-cyan-200">
              <FlowBox level={3}>اللاملموسية</FlowBox>
              <FlowBox level={3}>التلازمية</FlowBox>
              <FlowBox level={3}>التباين</FlowBox>
              <FlowBox level={3}>الفنائية</FlowBox>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>استراتيجيات تسويق الخدمات</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-cyan-50 rounded-md border-2 border-dashed border-cyan-200">
              <FlowBox level={3}>سلسلة الربح الخدمي</FlowBox>
              <FlowBox level={3}>إدارة التمايز الخدمي</FlowBox>
              <FlowBox level={3}>إدارة جودة الخدمة</FlowBox>
              <FlowBox level={3}>إدارة إنتاجية الخدمة</FlowBox>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>المزيج التسويقي الموسع (7Ps)</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-cyan-50 rounded-md border-2 border-dashed border-cyan-200">
                <FlowBox level={3}>الأفراد (People)</FlowBox>
                <FlowBox level={3}>العمليات (Process)</FlowBox>
                <FlowBox level={3}>الدليل المادي (Physical Evidence)</FlowBox>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};