import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { ShoppingCart, Target, TrendingUp, Handshake } from 'lucide-react';

const Definition = ({ text }: { text: string }) => (
  <div className="p-4 my-4 border-r-4 border-green-600 rounded-lg bg-green-50/50 shadow-sm">
    <blockquote className="text-slate-700 leading-relaxed italic">
      "{text}"
    </blockquote>
  </div>
);

const ImportancePoint = ({ number, title, children, icon: Icon }: { number: number; title: string; children: React.ReactNode; icon: React.ElementType }) => (
    <div className="flex items-start gap-4 p-4 bg-slate-50 rounded-lg border">
        <div className="flex-shrink-0 h-8 w-8 bg-green-100 text-green-700 font-bold rounded-full flex items-center justify-center mt-1">
            {number}
        </div>
        <div>
            <h4 className="font-bold text-lg text-slate-800 flex items-center gap-2">
                <Icon className="h-5 w-5 text-green-600" />
                {title}
            </h4>
            <p className="text-slate-700 leading-relaxed mt-1">{children}</p>
        </div>
    </div>
);

export const Chapter16Concept = () => {
  return (
    <div className="space-y-6">
      <Card className="bg-white shadow-none border-0">
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-slate-800 flex items-center gap-3">
            <ShoppingCart className="h-8 w-8 text-green-700" />
            مفهوم وأهمية المشتريات
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
          <p>
            تمثل وظيفة المشتريات عملية الحصول على المواد والخدمات والمعدات اللازمة لعمليات المنظمة. في الماضي، كان يُنظر إليها على أنها وظيفة إدارية بسيطة، ولكن اليوم، أصبحت جزءًا استراتيجيًا من إدارة سلسلة التوريد.
          </p>
          <Definition
            text="المشتريات هي وظيفة الحصول على المواد بالجودة المناسبة، والكمية المناسبة، في الوقت المناسب، بالسعر المناسب، ومن المصدر المناسب."
          />
          <p>
            هذا التعريف، المعروف بـ "الحقوق الخمسة للمشتريات" (The Five Rights of Purchasing)، يوضح الأبعاد المتعددة التي يجب على مديري المشتريات موازنتها لتحقيق أهدافهم.
          </p>
        </CardContent>
      </Card>

      <div className="pt-6 border-t">
        <h3 className="text-2xl font-bold text-slate-800 mb-4">أهمية وظيفة المشتريات</h3>
        <div className="space-y-4">
            <ImportancePoint number={1} icon={TrendingUp} title="التأثير على الربحية">
                تمثل تكلفة المواد المشتراة نسبة كبيرة من إجمالي تكاليف الشركة. أي تخفيض في تكاليف الشراء ينعكس مباشرة على زيادة الأرباح.
            </ImportancePoint>
            <ImportancePoint number={2} icon={Target} title="ضمان جودة المنتج النهائي">
                جودة المواد والمدخلات المشتراة تؤثر بشكل مباشر على جودة المنتج النهائي الذي تقدمه الشركة لعملائها.
            </ImportancePoint>
            <ImportancePoint number={3} icon={Handshake} title="بناء علاقات مع الموردين">
                تساهم المشتريات في بناء علاقات استراتيجية طويلة الأمد مع الموردين، مما يضمن استمرارية التوريد ويفتح الباب للابتكار المشترك.
            </ImportancePoint>
            <ImportancePoint number={4} icon={ShoppingCart} title="دعم العمليات التشغيلية">
                تضمن وظيفة المشتريات توفر المواد اللازمة في الوقت المناسب، مما يمنع توقف خطوط الإنتاج ويضمن استمرارية العمليات.
            </ImportancePoint>
        </div>
      </div>
    </div>
  );
};