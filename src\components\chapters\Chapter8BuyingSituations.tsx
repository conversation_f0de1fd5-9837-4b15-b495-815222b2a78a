import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Repeat, Edit, PlusCircle, type LucideIcon } from 'lucide-react';

const SituationCard = ({ icon: Icon, title, enTitle, description, color }: { icon: LucideIcon, title: string, enTitle: string, description: string, color: string }) => (
    <Card className={`h-full flex flex-col border-t-4 ${color} shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300`}>
        <CardHeader>
            <CardTitle className="flex items-center gap-3 text-2xl text-dark-gray">
                <Icon className={`h-8 w-8 ${color.replace('border-t-', 'text-')}`} />
                {title}
            </CardTitle>
            <p className="text-sm text-slate-500 -mt-2">{enTitle}</p>
        </CardHeader>
        <CardContent className="flex-grow">
            <p className="text-text-gray leading-relaxed">{description}</p>
        </CardContent>
    </Card>
);

export const Chapter8BuyingSituations = () => {
  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-dark-gray">أنواع مواقف الشراء الرئيسية</h3>
      <p className="text-text-gray leading-relaxed">
        يواجه المشتري الصناعي أنواعًا مختلفة من مواقف الشراء، والتي تتراوح من عمليات إعادة الشراء الروتينية إلى المهام الجديدة تمامًا. كل موقف يتطلب نهجًا مختلفًا من المسوق.
      </p>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 pt-4">
        <SituationCard
          icon={Repeat}
          title="إعادة الشراء المباشر"
          enTitle="Straight Rebuy"
          color="border-t-green-500"
          description="موقف شراء يعيد فيه المشتري طلب شيء ما بشكل روتيني دون أي تعديلات. يعتمد المجهزون الحاليون على الحفاظ على جودة المنتج والخدمة، بينما يحاول المجهزون الجدد تقديم قيمة مضافة لاختراق هذا الروتين."
        />
        <SituationCard
          icon={Edit}
          title="إعادة الشراء المعدل"
          enTitle="Modified Rebuy"
          color="border-t-orange-500"
          description="موقف شراء يرغب فيه المشتري في تعديل مواصفات المنتج، الأسعار، الشروط، أو المجهزين. يمثل هذا الموقف فرصة للمجهزين الجدد لتقديم عرض أفضل والفوز بالصفقة."
        />
        <SituationCard
          icon={PlusCircle}
          title="مهمة جديدة"
          enTitle="New Task"
          color="border-t-blue-500"
          description="موقف شراء يقوم فيه المشتري بشراء منتج أو خدمة لأول مرة. هذا هو التحدي والفرصة الأكبر للمسوق، حيث يتخذ المشتري أكبر عدد من القرارات في هذه الحالة."
        />
      </div>
    </div>
  );
};