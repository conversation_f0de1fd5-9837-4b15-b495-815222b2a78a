import React from 'react';
import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Globe, Building2, Users, User, type LucideIcon } from 'lucide-react';

const InfluenceCard = ({ icon: Icon, title, items, color }: { icon: LucideIcon, title: string, items: string[], color: string }) => (
    <div className={`p-4 rounded-lg shadow-md bg-white border-t-4 ${color}`}>
        <h5 className={`font-bold text-lg mb-3 flex items-center gap-2 ${color.replace('border-t-', 'text-').replace('-500', '-700')}`}>
            <Icon className="h-6 w-6" />
            {title}
        </h5>
        <ul className="space-y-2 text-sm text-slate-600">
            {items.map((item, index) => (
                <li key={index} className="flex items-start gap-2">
                    <span className="h-1.5 w-1.5 bg-slate-400 rounded-full mt-2 flex-shrink-0"></span>
                    <span>{item}</span>
                </li>
            ))}
        </ul>
    </div>
);

const InfluencesDiagram = () => (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-8 text-slate-800">المؤثرات الرئيسية على سلوك المشتري الصناعي</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            <InfluenceCard 
                icon={Globe}
                title="البيئية"
                items={["الطلب الاقتصادي", "تكلفة المال", "التطور التكنولوجي", "التطورات السياسية والتنظيمية", "التطورات التنافسية"]}
                color="border-t-purple-500"
            />
            <InfluenceCard 
                icon={Building2}
                title="التنظيمية"
                items={["الأهداف", "الاستراتيجيات", "الهيكل التنظيمي", "الأنظمة", "الإجراءات"]}
                color="border-t-blue-500"
            />
            <InfluenceCard 
                icon={Users}
                title="العلاقات البينية"
                items={["التأثير", "الخبرة", "السلطة", "الديناميكيات"]}
                color="border-t-green-500"
            />
            <InfluenceCard 
                icon={User}
                title="الفردية"
                items={["العمر/التعليم", "المنصب الوظيفي", "الدوافع", "الشخصية", "تحمل المخاطر"]}
                color="border-t-orange-500"
            />
        </div>
    </div>
);

export const Chapter8Influences = () => {
  return (
    <div className="space-y-6">
      <p className="text-slate-700 leading-relaxed">
        يتأثر المشترون الصناعيون بمجموعة واسعة من العوامل عند اتخاذ قرارات الشراء. يمكن تصنيف هذه المؤثرات إلى أربع فئات رئيسية: البيئية، التنظيمية، العلاقات البينية، والفردية.
      </p>
      <InfluencesDiagram />
    </div>
  );
};