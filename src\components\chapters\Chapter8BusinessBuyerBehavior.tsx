import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { ArrowLeft } from 'lucide-react';

const ModelBox = ({ title, items, className }: { title: string, items: string[], className?: string }) => (
    <div className={`flex-1 p-4 border rounded-lg shadow-sm ${className}`}>
        <h4 className="font-bold text-center mb-3 pb-2 border-b">{title}</h4>
        <ul className="space-y-2 text-sm">
            {items.map((item, index) => (
                <li key={index}>{item}</li>
            ))}
        </ul>
    </div>
);

const ModelArrow = () => (
    <div className="flex-shrink-0 flex items-center justify-center mx-4">
        <ArrowLeft className="h-8 w-8 text-slate-600" />
    </div>
);

const BusinessBuyerBehaviorDiagram = () => (
    <div dir="rtl" className="my-8 p-6 bg-slate-50 rounded-xl border">
        <h4 className="text-center font-bold text-xl mb-2 text-slate-800">شكل (8-1): نموذج سلوك المشتري الصناعي</h4>
        <div className="flex flex-col md:flex-row-reverse gap-4 items-stretch">
            <ModelBox 
                title="البيئة" 
                items={[
                    "المزيج التسويقي (المنتج، السعر، المكان، الترويج)",
                    "مثيرات أخرى (اقتصادية، تكنولوجية، سياسية، ثقافية، تنافسية)"
                ]}
                className="bg-blue-50 border-blue-200 text-blue-900"
            />
            <ModelArrow />
            <ModelBox 
                title="المنظمة المشترية (الصندوق الأسود)" 
                items={[
                    "مركز الشراء (عملية قرار الشراء)",
                    "المؤثرات التنظيمية، الشخصية، والعلاقات البينية"
                ]}
                className="bg-gray-700 border-gray-800 text-white"
            />
            <ModelArrow />
            <ModelBox 
                title="استجابة المشتري" 
                items={[
                    "اختيار المنتج أو الخدمة",
                    "اختيار المجهز",
                    "كميات الطلب",
                    "شروط وأوقات التسليم",
                    "شروط الخدمة والدفع"
                ]}
                className="bg-green-50 border-green-200 text-green-900"
            />
        </div>
    </div>
);

export const Chapter8BusinessBuyerBehavior = () => {
  return (
    <div className="space-y-6">
      <Card className="bg-white shadow-none border-0">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-slate-800">
            نموذج سلوك المشتري الصناعي
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-lg leading-relaxed text-slate-700">
          <p>
            لفهم كيفية اتخاذ القرارات في أسواق الأعمال، طور المسوقون نموذجًا لسلوك المشتري الصناعي. يبدأ هذا النموذج بالمثيرات التسويقية والبيئية التي تؤثر على المنظمة المشترية، والتي بدورها تنتج استجابات معينة.
          </p>
          <p>
            تمامًا كما هو الحال في سلوك المستهلك، يكمن التحدي في فهم ما يحدث داخل "الصندوق الأسود" للمنظمة المشترية لتحويل المثيرات إلى استجابات. يتكون هذا الصندوق الأسود من جزأين رئيسيين: مركز الشراء وعملية قرار الشراء، وكلاهما يتأثر بعوامل داخلية وخارجية.
          </p>
        </CardContent>
      </Card>
      <BusinessBuyerBehaviorDiagram />
    </div>
  );
};