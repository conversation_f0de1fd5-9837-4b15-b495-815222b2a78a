import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Chapter8Flowchart } from './Chapter8Flowchart';
import { Chapter8Introduction } from './Chapter8Introduction';
import { Chapter8Characteristics } from './Chapter8Characteristics';
import { Chapter8BusinessBuyerBehavior } from './Chapter8BusinessBuyerBehavior';
import { Chapter8BuyingSituations } from './Chapter8BuyingSituations';
import { Chapter8Participants } from './Chapter8Participants';
import { Chapter8Influences } from './Chapter8Influences';
import { Chapter8BuyingProcess } from './Chapter8BuyingProcess';
import { BookOpen, Share2, Building2, Users, GitCompareArrows, SlidersHorizontal, ListOrdered, TestTube2 } from 'lucide-react';
import { Quiz } from "@/components/Quiz";
import { quizzes } from "@/data/quizzes";

// Find the quiz for chapter 8
const chapter8Quiz = quizzes.find(q => q.chapterId === 8);

// Define sections for Chapter 8
export const chapter8Sections = [
  { value: "item-0", title: "هيكلية الفصل", icon: Share2, component: <Chapter8Flowchart /> },
  { value: "item-1", title: "مقدمة الفصل", icon: BookOpen, component: <Chapter8Introduction /> },
  { value: "item-2", title: "خصائص أسواق الأعمال", icon: Building2, component: <Chapter8Characteristics /> },
  { value: "item-3", title: "نموذج سلوك المشتري الصناعي", icon: GitCompareArrows, component: <Chapter8BusinessBuyerBehavior /> },
  { value: "item-4", title: "أنواع مواقف الشراء", icon: SlidersHorizontal, component: <Chapter8BuyingSituations /> },
  { value: "item-5", title: "المشاركون في عملية الشراء", icon: Users, component: <Chapter8Participants /> },
  { value: "item-6", title: "المؤثرات الرئيسية على المشترين", icon: SlidersHorizontal, component: <Chapter8Influences /> },
  { value: "item-7", title: "مراحل عملية الشراء الصناعي", icon: ListOrdered, component: <Chapter8BuyingProcess /> },
  {
    value: "item-quiz",
    title: "اختبر معلوماتك",
    icon: TestTube2,
    component: chapter8Quiz ? <Quiz questions={chapter8Quiz.questions} chapterId={8} /> : <p>لا يوجد اختبار متاح حاليًا.</p>,
  },
];

interface Chapter8Props {
  sections: typeof chapter8Sections;
  activeSectionValue?: string;
  isPresentationMode: boolean;
}

export const Chapter8 = ({ sections, activeSectionValue, isPresentationMode }: Chapter8Props) => {
  return (
    <Accordion
      type="single"
      collapsible
      defaultValue={isPresentationMode ? undefined : (activeSectionValue || "item-1")}
      value={isPresentationMode ? activeSectionValue : undefined}
      className="w-full space-y-4"
    >
      {sections.map((section) => (
        <AccordionItem key={section.value} value={section.value} className="border rounded-lg bg-white shadow-sm overflow-hidden">
          <AccordionTrigger className="p-6 text-2xl font-bold text-slate-700 hover:no-underline data-[state=open]:bg-slate-50">
            <div className="flex items-center gap-4">
              <section.icon className="h-7 w-7 text-gray-600 flex-shrink-0" />
              <span className="text-right">{section.title}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-6 pt-2 bg-white">
            {section.component}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};