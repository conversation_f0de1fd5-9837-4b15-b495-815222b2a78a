import React from 'react';
import { ArrowDown } from 'lucide-react';

const FlowBox = ({ children, className = '', level = 1 }: { children: React.ReactNode; className?: string; level?: number }) => {
  const levelClasses = {
    1: 'bg-slate-700 border-slate-900 text-white font-bold text-lg',
    2: 'bg-slate-300 border-slate-500 text-slate-800 font-semibold',
    3: 'bg-slate-200 border-slate-400 text-slate-700 text-sm',
  };
  return (
    <div className={`border-t-2 border-x-2 rounded-lg p-2 text-center shadow-lg flex items-center justify-center min-h-[45px] w-full transition-transform duration-200 hover:-translate-y-1 border-b-4 ${levelClasses[level]} ${className}`}>
      <span>{children}</span>
    </div>
  );
};

const VerticalArrow = () => <ArrowDown className="h-6 w-6 text-slate-400 mx-auto my-1" />;

export const Chapter15Flowchart = () => {
  return (
    <div dir="rtl" className="p-4 bg-slate-100 rounded-2xl border-2 border-slate-200 shadow-inner mb-8 font-sans">
      <h3 className="text-center font-bold text-2xl mb-6 text-slate-800">
        هيكلية الفصل الخامس عشر
      </h3>
      <div className="flex flex-col items-center">
        <FlowBox level={1} className="max-w-sm">المبيعات وإدارة قوى البيع</FlowBox>
        <VerticalArrow />
        
        <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-6">
          
          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>طبيعة البيع الشخصي</FlowBox>
            <VerticalArrow />
            <FlowBox level={3}>دور رجل البيع</FlowBox>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>إدارة قوى البيع</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-slate-50 rounded-md border-2 border-dashed border-slate-200">
              <FlowBox level={3}>تصميم الاستراتيجية والهيكل</FlowBox>
              <FlowBox level={3}>التوظيف والتدريب</FlowBox>
              <FlowBox level={3}>التعويض والإشراف</FlowBox>
              <FlowBox level={3}>التقييم</FlowBox>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-2">
            <FlowBox level={2}>عملية البيع الشخصي</FlowBox>
            <VerticalArrow />
            <div className="space-y-1 w-full p-2 bg-slate-50 rounded-md border-2 border-dashed border-slate-200">
              <FlowBox level={3}>البحث عن العملاء</FlowBox>
              <FlowBox level={3}>التحضير والمقاربة</FlowBox>
              <FlowBox level={3}>العرض والاعتراضات</FlowBox>
              <FlowBox level={3}>إغلاق الصفقة والمتابعة</FlowBox>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};